import Layout from '@/components/Layout';
import { flightCompanies } from '@/data/companies';
import { ExternalLink, Mail, Phone, Calendar, Star } from 'lucide-react';

export default function Directory() {
  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Airlines & Travel Agencies Directory
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl">
              Discover trusted flight booking companies in Bangladesh. Compare services, 
              contact information, and specialties to find the best option for your travel needs.
            </p>
          </div>
        </div>

        {/* Companies Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {flightCompanies.map((company) => (
              <div key={company.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                {/* Company Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {company.name}
                      </h3>
                      {company.established && (
                        <div className="flex items-center text-sm text-gray-500 mb-2">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>Est. {company.established}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {company.description}
                  </p>
                </div>

                {/* Contact Information */}
                <div className="p-6 border-b border-gray-100">
                  <h4 className="font-semibold text-gray-900 mb-3">Contact Information</h4>
                  <div className="space-y-2">
                    <a
                      href={company.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:text-blue-800 text-sm transition-colors"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Visit Website
                    </a>
                    
                    {company.email && (
                      <div className="flex items-center text-gray-600 text-sm">
                        <Mail className="h-4 w-4 mr-2" />
                        <span>{company.email}</span>
                      </div>
                    )}
                    
                    {company.phone && (
                      <div className="flex items-center text-gray-600 text-sm">
                        <Phone className="h-4 w-4 mr-2" />
                        <span>{company.phone}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Specialties */}
                <div className="p-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Specialties</h4>
                  <div className="flex flex-wrap gap-2">
                    {company.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Action Button */}
                <div className="p-6 pt-0">
                  <a
                    href={company.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block"
                  >
                    Book with {company.name}
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Need Help Choosing?
              </h2>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Not sure which airline or travel agency to choose? Use our flight search tool 
                to compare prices and find the best deals across all these trusted providers.
              </p>
              <a
                href="/search"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
              >
                Compare Flight Prices
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
