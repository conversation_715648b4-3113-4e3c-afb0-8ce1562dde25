import Link from 'next/link';
import Layout from '@/components/Layout';
import { Search, Building2, TrendingUp, Shield } from 'lucide-react';

export default function Home() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Find the Best Flight Deals in Bangladesh
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              Compare prices from top airlines and travel agencies. Save money and time with BD Flight Finder.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/search"
                className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors inline-flex items-center justify-center"
              >
                <Search className="mr-2 h-5 w-5" />
                Search Flights
              </Link>
              <Link
                href="/directory"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center"
              >
                <Building2 className="mr-2 h-5 w-5" />
                View Airlines
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose BD Flight Finder?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We make flight booking simple, transparent, and affordable for everyone in Bangladesh.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Easy Search</h3>
              <p className="text-gray-600">
                Find flights quickly with our intuitive search interface. Compare prices across multiple platforms instantly.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Best Prices</h3>
              <p className="text-gray-600">
                We compare prices from all major airlines and travel agencies to ensure you get the best deal available.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Trusted Partners</h3>
              <p className="text-gray-600">
                All our partner airlines and agencies are verified and trusted providers in the Bangladesh market.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Routes Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Popular Flight Routes
            </h2>
            <p className="text-xl text-gray-600">
              Discover the most searched flight routes from Bangladesh
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { from: 'Dhaka', to: 'Dubai', price: 'From ৳45,000' },
              { from: 'Dhaka', to: 'Kolkata', price: 'From ৳12,000' },
              { from: 'Dhaka', to: 'Singapore', price: 'From ৳55,000' },
              { from: 'Chittagong', to: 'Dhaka', price: 'From ৳8,000' },
              { from: 'Dhaka', to: 'Bangkok', price: 'From ৳35,000' },
              { from: 'Dhaka', to: 'London', price: 'From ৳85,000' },
            ].map((route, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">{route.from} → {route.to}</h3>
                    <p className="text-blue-600 font-medium">{route.price}</p>
                  </div>
                </div>
                <Link
                  href="/search"
                  className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                >
                  Search this route →
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>
    </Layout>
  );
}
