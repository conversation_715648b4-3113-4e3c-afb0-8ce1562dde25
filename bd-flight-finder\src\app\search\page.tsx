'use client';

import { useState } from 'react';
import Layout from '@/components/Layout';
import FlightSearchForm from '@/components/FlightSearchForm';
import FlightResults, { FlightResult } from '@/components/FlightResults';
import { cities } from '@/data/companies';

// Mock flight data generator
const generateMockFlights = (from: string, to: string, departDate: string): FlightResult[] => {
  const fromCity = cities.find(c => c.code === from);
  const toCity = cities.find(c => c.code === to);
  
  if (!fromCity || !toCity) return [];

  const airlines = [
    'Biman Bangladesh Airlines',
    'Emirates',
    'Qatar Airways',
    'Singapore Airlines',
    'Thai Airways',
    'Malaysia Airlines',
    'Air India',
    'IndiGo'
  ];

  const providers = [
    { name: 'Flight Expert', url: 'https://www.flightexpert.com' },
    { name: 'Gozayaan', url: 'https://gozayaan.com' },
    { name: 'ShareTrip', url: 'https://sharetrip.net' },
    { name: 'Amy BD', url: 'https://www.amybd.com' }
  ];

  const flights: FlightResult[] = [];
  
  // Generate 8-12 mock flights
  const numFlights = Math.floor(Math.random() * 5) + 8;
  
  for (let i = 0; i < numFlights; i++) {
    const airline = airlines[Math.floor(Math.random() * airlines.length)];
    const provider = providers[Math.floor(Math.random() * providers.length)];
    const stops = Math.random() < 0.3 ? 0 : Math.random() < 0.7 ? 1 : 2;
    
    // Generate realistic times
    const departHour = Math.floor(Math.random() * 24);
    const departMinute = Math.floor(Math.random() * 4) * 15; // 0, 15, 30, 45
    const duration = stops === 0 ? 
      Math.floor(Math.random() * 4) + 2 : // 2-6 hours for direct
      Math.floor(Math.random() * 8) + 6;   // 6-14 hours for connecting
    
    const arriveHour = (departHour + duration) % 24;
    const arriveMinute = departMinute;
    
    // Generate realistic prices based on route
    let basePrice = 15000; // Base price in BDT
    if (toCity.country !== 'Bangladesh') {
      basePrice = toCity.country === 'India' ? 12000 : 
                  toCity.country === 'UAE' ? 45000 :
                  toCity.country === 'Qatar' ? 42000 :
                  toCity.country === 'Malaysia' ? 35000 :
                  toCity.country === 'Singapore' ? 55000 :
                  toCity.country === 'Thailand' ? 35000 :
                  toCity.country === 'UK' ? 85000 :
                  toCity.country === 'USA' ? 120000 : 50000;
    }
    
    const priceVariation = Math.random() * 0.4 - 0.2; // ±20%
    const finalPrice = Math.round(basePrice * (1 + priceVariation));
    
    flights.push({
      id: `flight-${i}`,
      airline,
      from: fromCity.name,
      to: toCity.name,
      departTime: `${departHour.toString().padStart(2, '0')}:${departMinute.toString().padStart(2, '0')}`,
      arriveTime: `${arriveHour.toString().padStart(2, '0')}:${arriveMinute.toString().padStart(2, '0')}`,
      duration: `${duration}h ${Math.floor(Math.random() * 60)}m`,
      price: finalPrice,
      currency: '৳',
      provider: provider.name,
      providerUrl: provider.url,
      stops,
      aircraft: Math.random() < 0.5 ? 'Boeing 737' : 'Airbus A320'
    });
  }
  
  return flights.sort((a, b) => a.price - b.price);
};

export default function SearchPage() {
  const [searchResults, setSearchResults] = useState<FlightResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (searchData: any) => {
    setIsLoading(true);
    setHasSearched(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const results = generateMockFlights(searchData.from, searchData.to, searchData.departDate);
    setSearchResults(results);
    setIsLoading(false);
  };

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Search Flights
            </h1>
            <p className="text-xl text-gray-600">
              Find and compare the best flight deals from top airlines and travel agencies.
            </p>
          </div>
        </div>

        {/* Search Form */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <FlightSearchForm onSearch={handleSearch} isLoading={isLoading} />
        </div>

        {/* Results */}
        {hasSearched && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            <FlightResults results={searchResults} isLoading={isLoading} />
          </div>
        )}

        {/* Tips Section */}
        {!hasSearched && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Flight Search Tips</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-lg mb-3">Best Time to Book</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Book domestic flights 1-3 weeks in advance</li>
                    <li>• Book international flights 2-8 weeks in advance</li>
                    <li>• Tuesday and Wednesday often have lower prices</li>
                    <li>• Avoid booking during peak seasons</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-3">Money-Saving Tips</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Compare prices across multiple providers</li>
                    <li>• Consider nearby airports for better deals</li>
                    <li>• Be flexible with your travel dates</li>
                    <li>• Sign up for price alerts</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
