'use client';

import { useState } from 'react';
import { Clock, Plane, ExternalLink, Filter, ArrowUpDown } from 'lucide-react';

export interface FlightResult {
  id: string;
  airline: string;
  from: string;
  to: string;
  departTime: string;
  arriveTime: string;
  duration: string;
  price: number;
  currency: string;
  provider: string;
  providerUrl: string;
  stops: number;
  aircraft?: string;
}

interface FlightResultsProps {
  results: FlightResult[];
  isLoading: boolean;
}

export default function FlightResults({ results, isLoading }: FlightResultsProps) {
  const [sortBy, setSortBy] = useState<'price' | 'duration' | 'departure'>('price');
  const [filterStops, setFilterStops] = useState<'all' | 'direct' | 'one-stop'>('all');

  // Filter and sort results
  const filteredAndSortedResults = results
    .filter(flight => {
      if (filterStops === 'direct') return flight.stops === 0;
      if (filterStops === 'one-stop') return flight.stops === 1;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price;
        case 'duration':
          return parseInt(a.duration) - parseInt(b.duration);
        case 'departure':
          return a.departTime.localeCompare(b.departTime);
        default:
          return 0;
      }
    });

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Searching for flights...</h3>
          <p className="text-gray-600">Please wait while we find the best deals for you.</p>
        </div>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <Plane className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No flights found</h3>
        <p className="text-gray-600">Try adjusting your search criteria or dates.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Sorting */}
      <div className="bg-white rounded-lg shadow-lg p-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filter by stops:</span>
            </div>
            <select
              value={filterStops}
              onChange={(e) => setFilterStops(e.target.value as 'all' | 'direct' | 'one-stop')}
              className="border border-gray-300 rounded px-3 py-1 text-sm"
            >
              <option value="all">All flights</option>
              <option value="direct">Direct only</option>
              <option value="one-stop">1 stop max</option>
            </select>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <ArrowUpDown className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Sort by:</span>
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'price' | 'duration' | 'departure')}
              className="border border-gray-300 rounded px-3 py-1 text-sm"
            >
              <option value="price">Price (Low to High)</option>
              <option value="duration">Duration</option>
              <option value="departure">Departure Time</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-4">
        <div className="text-sm text-gray-600">
          Showing {filteredAndSortedResults.length} of {results.length} flights
        </div>

        {filteredAndSortedResults.map((flight) => (
          <div key={flight.id} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
              {/* Flight Info */}
              <div className="flex-1">
                <div className="flex items-center space-x-4 mb-3">
                  <div className="font-semibold text-lg text-gray-900">{flight.airline}</div>
                  {flight.aircraft && (
                    <div className="text-sm text-gray-500">{flight.aircraft}</div>
                  )}
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{flight.departTime}</div>
                    <div className="text-sm text-gray-600">{flight.from}</div>
                  </div>

                  <div className="flex-1 text-center">
                    <div className="flex items-center justify-center space-x-2 mb-1">
                      <div className="h-px bg-gray-300 flex-1"></div>
                      <Plane className="h-4 w-4 text-gray-400" />
                      <div className="h-px bg-gray-300 flex-1"></div>
                    </div>
                    <div className="text-sm text-gray-600 flex items-center justify-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {flight.duration}
                    </div>
                    {flight.stops > 0 && (
                      <div className="text-xs text-orange-600 mt-1">
                        {flight.stops} stop{flight.stops > 1 ? 's' : ''}
                      </div>
                    )}
                    {flight.stops === 0 && (
                      <div className="text-xs text-green-600 mt-1">Direct</div>
                    )}
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{flight.arriveTime}</div>
                    <div className="text-sm text-gray-600">{flight.to}</div>
                  </div>
                </div>
              </div>

              {/* Price and Book */}
              <div className="text-center lg:text-right lg:ml-8">
                <div className="text-3xl font-bold text-blue-600 mb-1">
                  {flight.currency}{flight.price.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600 mb-3">via {flight.provider}</div>
                <a
                  href={flight.providerUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center"
                >
                  Book Now
                  <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
